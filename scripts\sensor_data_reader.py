#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
传感器数据读取节点
用于获取和处理机器人传感器数据，包括激光雷达、摄像头和里程计数据
"""

import rospy
import cv2
import numpy as np
from sensor_msgs.msg import LaserScan, Image, CompressedImage
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Twist
from cv_bridge import CvBridge
import tf
import math

class SensorDataReader:
    def __init__(self):
        """初始化传感器数据读取器"""
        rospy.init_node('sensor_data_reader', anonymous=True)
        
        # 初始化CV桥接器
        self.bridge = CvBridge()
        
        # 初始化TF监听器
        self.tf_listener = tf.TransformListener()
        
        # 传感器数据存储
        self.laser_data = None
        self.camera_image = None
        self.odom_data = None
        
        # 订阅传感器话题
        self.laser_sub = rospy.Subscriber('/scan', LaserScan, self.laser_callback)
        self.camera_sub = rospy.Subscriber('/robot_camera/image_raw', Image, self.camera_callback)
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        
        # 发布处理后的数据（可选）
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        
        rospy.loginfo("传感器数据读取器已启动")
        
    def laser_callback(self, data):
        """激光雷达数据回调函数"""
        self.laser_data = data
        
        # 处理激光雷达数据
        ranges = np.array(data.ranges)
        
        # 过滤无效数据
        valid_ranges = ranges[(ranges >= data.range_min) & (ranges <= data.range_max)]
        
        if len(valid_ranges) > 0:
            min_distance = np.min(valid_ranges)
            max_distance = np.max(valid_ranges)
            avg_distance = np.mean(valid_ranges)
            
            rospy.loginfo_throttle(2, f"激光雷达数据 - 最小距离: {min_distance:.2f}m, "
                                    f"最大距离: {max_distance:.2f}m, "
                                    f"平均距离: {avg_distance:.2f}m")
            
            # 简单的避障逻辑示例
            self.simple_obstacle_avoidance(ranges, data.angle_min, data.angle_increment)
    
    def camera_callback(self, data):
        """摄像头数据回调函数"""
        try:
            # 将ROS图像消息转换为OpenCV图像
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            self.camera_image = cv_image
            
            # 简单的图像处理示例
            self.process_camera_image(cv_image)
            
        except Exception as e:
            rospy.logerr(f"摄像头数据处理错误: {e}")
    
    def odom_callback(self, data):
        """里程计数据回调函数"""
        self.odom_data = data
        
        # 获取位置信息
        x = data.pose.pose.position.x
        y = data.pose.pose.position.y
        z = data.pose.pose.position.z
        
        # 获取方向信息（四元数转欧拉角）
        orientation = data.pose.pose.orientation
        euler = tf.transformations.euler_from_quaternion([
            orientation.x, orientation.y, orientation.z, orientation.w
        ])
        yaw = euler[2]
        
        # 获取速度信息
        linear_vel = data.twist.twist.linear
        angular_vel = data.twist.twist.angular
        
        rospy.loginfo_throttle(2, f"机器人位置: x={x:.2f}, y={y:.2f}, yaw={math.degrees(yaw):.1f}°")
        rospy.loginfo_throttle(2, f"机器人速度: 线速度={linear_vel.x:.2f}m/s, 角速度={angular_vel.z:.2f}rad/s")
    
    def process_camera_image(self, image):
        """处理摄像头图像"""
        # 获取图像尺寸
        height, width, channels = image.shape
        
        # 简单的颜色检测示例（检测红色物体）
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 定义红色的HSV范围
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        # 创建掩码
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = mask1 + mask2
        
        # 计算红色像素的数量
        red_pixels = cv2.countNonZero(mask)
        total_pixels = width * height
        red_percentage = (red_pixels / total_pixels) * 100
        
        if red_percentage > 1.0:  # 如果红色像素超过1%
            rospy.loginfo_throttle(2, f"检测到红色物体，占比: {red_percentage:.2f}%")
    
    def simple_obstacle_avoidance(self, ranges, angle_min, angle_increment):
        """简单的避障算法"""
        # 获取前方、左侧、右侧的距离
        num_readings = len(ranges)
        
        # 前方区域（中间1/3）
        front_start = num_readings // 3
        front_end = 2 * num_readings // 3
        front_ranges = ranges[front_start:front_end]
        
        # 左侧区域
        left_ranges = ranges[front_end:]
        
        # 右侧区域
        right_ranges = ranges[:front_start]
        
        # 过滤无效值
        front_ranges = front_ranges[np.isfinite(front_ranges)]
        left_ranges = left_ranges[np.isfinite(left_ranges)]
        right_ranges = right_ranges[np.isfinite(right_ranges)]
        
        # 计算最小距离
        front_min = np.min(front_ranges) if len(front_ranges) > 0 else float('inf')
        left_min = np.min(left_ranges) if len(left_ranges) > 0 else float('inf')
        right_min = np.min(right_ranges) if len(right_ranges) > 0 else float('inf')
        
        # 避障阈值
        obstacle_threshold = 1.0  # 1米
        
        # 创建运动命令
        cmd = Twist()
        
        if front_min < obstacle_threshold:
            # 前方有障碍物，停止前进并转向
            cmd.linear.x = 0.0
            if left_min > right_min:
                cmd.angular.z = 0.5  # 向左转
                rospy.loginfo_throttle(1, "前方有障碍物，向左转")
            else:
                cmd.angular.z = -0.5  # 向右转
                rospy.loginfo_throttle(1, "前方有障碍物，向右转")
        else:
            # 前方无障碍物，正常前进
            cmd.linear.x = 0.2
            cmd.angular.z = 0.0
        
        # 发布运动命令（仅在演示模式下）
        # self.cmd_vel_pub.publish(cmd)
    
    def get_sensor_summary(self):
        """获取传感器数据摘要"""
        summary = {
            'laser_available': self.laser_data is not None,
            'camera_available': self.camera_image is not None,
            'odom_available': self.odom_data is not None
        }
        
        if self.laser_data:
            ranges = np.array(self.laser_data.ranges)
            valid_ranges = ranges[(ranges >= self.laser_data.range_min) & 
                                (ranges <= self.laser_data.range_max)]
            if len(valid_ranges) > 0:
                summary['laser_min_distance'] = np.min(valid_ranges)
                summary['laser_avg_distance'] = np.mean(valid_ranges)
        
        if self.odom_data:
            summary['robot_x'] = self.odom_data.pose.pose.position.x
            summary['robot_y'] = self.odom_data.pose.pose.position.y
            
        return summary
    
    def run(self):
        """运行传感器数据读取器"""
        rate = rospy.Rate(10)  # 10Hz
        
        while not rospy.is_shutdown():
            # 定期打印传感器状态
            summary = self.get_sensor_summary()
            rospy.loginfo_throttle(5, f"传感器状态: {summary}")
            
            rate.sleep()

if __name__ == '__main__':
    try:
        sensor_reader = SensorDataReader()
        sensor_reader.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("传感器数据读取器已停止")
