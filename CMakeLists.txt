cmake_minimum_required(VERSION 3.0.2)
project(robot_navigation)

## 查找catkin宏和库
find_package(catkin REQUIRED COMPONENTS
  rospy
  roscpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf
  tf2
  tf2_ros
  urdf
  xacro
  gazebo_ros
)

## 系统依赖
find_package(Boost REQUIRED COMPONENTS system)

## catkin特定配置
catkin_package(
  INCLUDE_DIRS include
  LIBRARIES ${PROJECT_NAME}
  CATKIN_DEPENDS 
    rospy 
    roscpp 
    std_msgs 
    sensor_msgs 
    geometry_msgs 
    nav_msgs 
    tf 
    tf2 
    tf2_ros 
    urdf 
    xacro 
    gazebo_ros
  DEPENDS system_lib
)

## 指定额外的头文件位置
include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

## 声明C++库
# add_library(${PROJECT_NAME}
#   src/${PROJECT_NAME}/robot_navigation.cpp
# )

## 添加cmake目标依赖
# add_dependencies(${PROJECT_NAME} ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## 指定库链接
# target_link_libraries(${PROJECT_NAME}
#   ${catkin_LIBRARIES}
# )

## 声明C++可执行文件
# add_executable(${PROJECT_NAME}_node src/robot_navigation_node.cpp)

## 重命名C++可执行文件
# set_target_properties(${PROJECT_NAME}_node PROPERTIES OUTPUT_NAME node PREFIX "")

## 添加cmake目标依赖
# add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## 指定可执行文件链接的库
# target_link_libraries(${PROJECT_NAME}_node
#   ${catkin_LIBRARIES}
# )

## 安装Python脚本
catkin_install_python(PROGRAMS
  scripts/robot_controller.py
  scripts/sensor_data_reader.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

## 安装启动文件
install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  PATTERN ".svn" EXCLUDE
)

## 安装配置文件
install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
  PATTERN ".svn" EXCLUDE
)

## 安装URDF文件
install(DIRECTORY urdf/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/urdf
  PATTERN ".svn" EXCLUDE
)

## 安装世界文件
install(DIRECTORY worlds/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/worlds
  PATTERN ".svn" EXCLUDE
)

## 安装网格文件
install(DIRECTORY meshes/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/meshes
  PATTERN ".svn" EXCLUDE
)

## 安装地图文件
install(DIRECTORY maps/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/maps
  PATTERN ".svn" EXCLUDE
)
