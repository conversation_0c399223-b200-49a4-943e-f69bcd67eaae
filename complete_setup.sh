#!/bin/bash

# 完整的机器人导航项目设置脚本
# 在WSL Ubuntu环境中运行

set -e  # 遇到错误时退出

echo "=========================================="
echo "机器人导航项目完整设置脚本"
echo "=========================================="

# 检查ROS环境
if [ -z "$ROS_DISTRO" ]; then
    echo "设置ROS环境..."
    source /opt/ros/noetic/setup.bash
    export ROS_DISTRO=noetic
fi

echo "当前ROS版本: $ROS_DISTRO"

# 创建catkin工作空间
CATKIN_WS="$HOME/catkin_ws"
echo "创建catkin工作空间: $CATKIN_WS"

mkdir -p $CATKIN_WS/src
cd $CATKIN_WS

# 初始化工作空间（如果还没有初始化）
if [ ! -f "src/CMakeLists.txt" ]; then
    echo "初始化catkin工作空间..."
    catkin_make
fi

# 创建项目目录
PROJECT_DIR="$CATKIN_WS/src/robot_navigation"
echo "创建项目目录: $PROJECT_DIR"
mkdir -p $PROJECT_DIR

# 创建子目录
mkdir -p $PROJECT_DIR/{launch,urdf,worlds,config/{navigation,slam,rviz},scripts,maps,meshes}

echo "创建package.xml..."
cat > $PROJECT_DIR/package.xml << 'EOF'
<?xml version="1.0"?>
<package format="2">
  <name>robot_navigation</name>
  <version>1.0.0</version>
  <description>机器人设计与导航仿真项目</description>

  <maintainer email="<EMAIL>">Student</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- 构建依赖 -->
  <build_depend>rospy</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>urdf</build_depend>
  <build_depend>xacro</build_depend>
  <build_depend>gazebo_ros</build_depend>

  <!-- 运行时依赖 -->
  <exec_depend>rospy</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>urdf</exec_depend>
  <exec_depend>xacro</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  <exec_depend>gazebo_ros_control</exec_depend>
  <exec_depend>gazebo_plugins</exec_depend>
  <exec_depend>move_base</exec_depend>
  <exec_depend>amcl</exec_depend>
  <exec_depend>map_server</exec_depend>
  <exec_depend>gmapping</exec_depend>
  <exec_depend>dwa_local_planner</exec_depend>
  <exec_depend>global_planner</exec_depend>
  <exec_depend>rviz</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>joint_state_publisher_gui</exec_depend>
  <exec_depend>teleop_twist_keyboard</exec_depend>

  <export>
  </export>
</package>
EOF

echo "创建CMakeLists.txt..."
cat > $PROJECT_DIR/CMakeLists.txt << 'EOF'
cmake_minimum_required(VERSION 3.0.2)
project(robot_navigation)

find_package(catkin REQUIRED COMPONENTS
  rospy
  roscpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf
  tf2
  tf2_ros
  urdf
  xacro
  gazebo_ros
)

catkin_package(
  CATKIN_DEPENDS 
    rospy 
    roscpp 
    std_msgs 
    sensor_msgs 
    geometry_msgs 
    nav_msgs 
    tf 
    tf2 
    tf2_ros 
    urdf 
    xacro 
    gazebo_ros
)

include_directories(
  ${catkin_INCLUDE_DIRS}
)

catkin_install_python(PROGRAMS
  scripts/robot_controller.py
  scripts/sensor_data_reader.py
  scripts/test_setup.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
)

install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
)

install(DIRECTORY urdf/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/urdf
)

install(DIRECTORY worlds/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/worlds
)

install(DIRECTORY maps/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/maps
)
EOF

echo "创建简单的URDF机器人模型..."
cat > $PROJECT_DIR/urdf/robot.urdf << 'EOF'
<?xml version="1.0"?>
<robot name="mobile_robot">

  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>

  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>

  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>

  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>

  <link name="base_footprint"/>

  <joint name="base_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="base_link"/>
    <origin xyz="0.0 0.0 0.1" rpy="0 0 0"/>
  </joint>

  <link name="base_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.6 0.4 0.2"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.6 0.4 0.2"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="1.5708 0 1.5708"/>
      <mass value="5.0"/>
      <inertia ixx="0.0395" ixy="0.0" ixz="0.0" iyy="0.106" iyz="0.0" izz="0.106"/>
    </inertial>
  </link>

  <joint name="left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="left_wheel"/>
    <origin xyz="0.0 0.25 -0.05" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
  </joint>

  <link name="left_wheel">
    <visual>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.1" length="0.05"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.1" length="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.00208" ixy="0.0" ixz="0.0" iyy="0.00208" iyz="0.0" izz="0.0025"/>
    </inertial>
  </link>

  <joint name="right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="right_wheel"/>
    <origin xyz="0.0 -0.25 -0.05" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
  </joint>

  <link name="right_wheel">
    <visual>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.1" length="0.05"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.1" length="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.00208" ixy="0.0" ixz="0.0" iyy="0.00208" iyz="0.0" izz="0.0025"/>
    </inertial>
  </link>

  <joint name="lidar_joint" type="fixed">
    <parent link="base_link"/>
    <child link="lidar_link"/>
    <origin xyz="0.2 0 0.15" rpy="0 0 0"/>
  </joint>

  <link name="lidar_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.000167" ixy="0.0" ixz="0.0" iyy="0.000167" iyz="0.0" izz="0.000125"/>
    </inertial>
  </link>

  <joint name="camera_joint" type="fixed">
    <parent link="base_link"/>
    <child link="camera_link"/>
    <origin xyz="0.25 0 0.125" rpy="0 0 0"/>
  </joint>

  <link name="camera_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.05 0.05 0.05"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.05 0.05 0.05"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.000042" ixy="0.0" ixz="0.0" iyy="0.000042" iyz="0.0" izz="0.000042"/>
    </inertial>
  </link>

</robot>
EOF

echo "创建robot_description.launch..."
cat > $PROJECT_DIR/launch/robot_description.launch << 'EOF'
<?xml version="1.0"?>
<launch>
  <arg name="model" default="$(find robot_navigation)/urdf/robot.urdf"/>
  <arg name="gui" default="true"/>

  <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>

  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher">
    <param name="publish_frequency" type="double" value="50.0"/>
  </node>

  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <param name="use_gui" value="$(arg gui)"/>
  </node>

  <node name="rviz" pkg="rviz" type="rviz" required="true"/>

</launch>
EOF

echo "创建测试脚本..."
cat > $PROJECT_DIR/scripts/test_setup.py << 'EOF'
#!/usr/bin/env python3

import os
import subprocess

def check_ros():
    print("检查ROS环境...")
    ros_distro = os.environ.get('ROS_DISTRO')
    if ros_distro:
        print(f"✓ ROS版本: {ros_distro}")
        return True
    else:
        print("✗ 未检测到ROS环境")
        return False

def check_packages():
    print("检查ROS包...")
    packages = ['gazebo_ros', 'navigation', 'rviz', 'robot_state_publisher']
    for pkg in packages:
        try:
            result = subprocess.run(['rospack', 'find', pkg], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {pkg}")
            else:
                print(f"✗ {pkg}")
        except:
            print(f"✗ {pkg} (检查失败)")

if __name__ == '__main__':
    print("=== 机器人导航项目环境检查 ===")
    check_ros()
    check_packages()
    print("检查完成！")
EOF

# 设置脚本权限
chmod +x $PROJECT_DIR/scripts/*.py

# 编译项目
echo "编译项目..."
cd $CATKIN_WS
catkin_make

if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "✓ 项目设置成功！"
    echo "=========================================="
    echo ""
    echo "下一步操作："
    echo "1. source ~/catkin_ws/devel/setup.bash"
    echo "2. roslaunch robot_navigation robot_description.launch"
    echo ""
    echo "或者运行测试："
    echo "rosrun robot_navigation test_setup.py"
    echo ""
    
    # 自动设置环境
    echo "source ~/catkin_ws/devel/setup.bash" >> ~/.bashrc
    echo "已将工作空间添加到 ~/.bashrc"
    
else
    echo "✗ 编译失败，请检查错误信息"
    exit 1
fi

echo "设置完成！"
EOF
