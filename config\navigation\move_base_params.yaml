# move_base参数配置

# 控制器频率
controller_frequency: 20.0

# 规划器频率
planner_frequency: 1.0

# 全局规划器
base_global_planner: "global_planner/GlobalPlanner"

# 局部规划器
base_local_planner: "dwa_local_planner/DWAPlannerROS"

# 恢复行为
recovery_behaviors:
  - {name: conservative_reset, type: clear_costmap_recovery/ClearCostmapRecovery}
  - {name: rotate_recovery, type: rotate_recovery/RotateRecovery}
  - {name: aggressive_reset, type: clear_costmap_recovery/ClearCostmapRecovery}

# 恢复行为参数
conservative_reset:
  reset_distance: 3.0

aggressive_reset:
  reset_distance: 1.84

# 控制器耐心度
controller_patience: 3.0

# 规划器耐心度
planner_patience: 5.0

# 保守重置距离
conservative_reset_dist: 3.0

# 振荡超时
oscillation_timeout: 0.0

# 振荡距离
oscillation_distance: 0.5

# 关机代价地图
shutdown_costmaps: false

# 清除旋转
clearing_rotation_allowed: true

# 恢复行为启用
recovery_behavior_enabled: true
