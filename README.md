# 机器人设计与导航仿真项目

这个项目实现了一个完整的机器人设计和导航仿真系统，包括：
- 使用URDF创建机器人模型
- 在Rviz和Gazebo中显示机器人
- 搭建仿真环境
- 实现SLAM建图、定位和路径规划

## 环境要求

### 方案一：WSL2 + Ubuntu + ROS Noetic（推荐）
1. 安装WSL2和Ubuntu 20.04
2. 安装ROS Noetic
3. 安装必要的ROS包

### 方案二：Docker（简单快速）
使用预配置的ROS Docker镜像

### 方案三：虚拟机
在VMware或VirtualBox中安装Ubuntu 20.04

## 快速开始

### 1. 环境安装（WSL2方案）

```bash
# 在Windows PowerShell中安装WSL2
wsl --install -d Ubuntu-20.04

# 进入WSL2 Ubuntu环境
wsl

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装ROS Noetic
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
sudo apt install curl -y
curl -s https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | sudo apt-key add -
sudo apt update
sudo apt install ros-noetic-desktop-full -y

# 设置环境
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
source ~/.bashrc

# 安装依赖工具
sudo apt install python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool build-essential -y
sudo rosdep init
rosdep update

# 安装项目所需的ROS包
sudo apt install ros-noetic-gazebo-ros-pkgs ros-noetic-gazebo-ros-control -y
sudo apt install ros-noetic-navigation ros-noetic-slam-gmapping -y
sudo apt install ros-noetic-move-base ros-noetic-amcl -y
sudo apt install ros-noetic-map-server ros-noetic-dwa-local-planner -y
```

### 2. 创建工作空间

```bash
# 创建catkin工作空间
mkdir -p ~/catkin_ws/src
cd ~/catkin_ws/
catkin_make

# 设置工作空间环境
echo "source ~/catkin_ws/devel/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### 3. 克隆项目

```bash
cd ~/catkin_ws/src
git clone <your-repo-url> robot_navigation
# 或者直接复制项目文件到 ~/catkin_ws/src/robot_navigation/
```

### 4. 编译项目

```bash
cd ~/catkin_ws
catkin_make
source devel/setup.bash
```

## 项目结构

```
robot_navigation/
├── CMakeLists.txt
├── package.xml
├── launch/                 # 启动文件
│   ├── robot_description.launch
│   ├── gazebo_world.launch
│   ├── navigation.launch
│   └── slam.launch
├── urdf/                   # 机器人模型文件
│   ├── robot.urdf
│   ├── robot.xacro
│   └── materials.xacro
├── meshes/                 # 3D模型文件
├── worlds/                 # Gazebo世界文件
│   └── simple_world.world
├── maps/                   # 地图文件
├── config/                 # 配置文件
│   ├── navigation/
│   ├── slam/
│   └── rviz/
├── scripts/                # Python脚本
└── src/                    # C++源码
```

## 使用方法

### 1. 在Rviz中查看机器人模型

```bash
# 启动机器人描述
roslaunch robot_navigation robot_description.launch

# 在新终端中启动Rviz
rviz -d $(rospack find robot_navigation)/config/rviz/robot_model.rviz
```

### 2. 在Gazebo中仿真

```bash
# 启动Gazebo仿真环境
roslaunch robot_navigation gazebo_world.launch

# 控制机器人移动（新终端）
rosrun teleop_twist_keyboard teleop_twist_keyboard.py
```

### 3. SLAM建图

```bash
# 启动SLAM
roslaunch robot_navigation slam.launch

# 启动Rviz查看建图过程
rviz -d $(rospack find robot_navigation)/config/rviz/slam.rviz

# 控制机器人移动进行建图
rosrun teleop_twist_keyboard teleop_twist_keyboard.py

# 保存地图
rosrun map_server map_saver -f ~/catkin_ws/src/robot_navigation/maps/my_map
```

### 4. 自主导航

```bash
# 启动导航系统
roslaunch robot_navigation navigation.launch

# 启动Rviz进行导航
rviz -d $(rospack find robot_navigation)/config/rviz/navigation.rviz
```

## 故障排除

### 常见问题
1. **Gazebo启动慢或卡死**：首次启动会下载模型，请耐心等待
2. **找不到包**：确保已经source了工作空间环境
3. **权限问题**：确保有足够的权限访问设备

### 调试命令
```bash
# 检查ROS环境
echo $ROS_DISTRO
roscore &
rostopic list

# 检查节点状态
rosnode list
rosnode info /node_name

# 查看话题数据
rostopic echo /topic_name
rostopic hz /topic_name
```

## 学习资源

- [ROS官方教程](http://wiki.ros.org/ROS/Tutorials)
- [URDF教程](http://wiki.ros.org/urdf/Tutorials)
- [Gazebo教程](http://gazebosim.org/tutorials)
- [Navigation Stack教程](http://wiki.ros.org/navigation/Tutorials)
