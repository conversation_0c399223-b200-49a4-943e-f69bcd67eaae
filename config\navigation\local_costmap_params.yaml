# 局部代价地图参数配置

local_costmap:
  # 全局参数
  global_frame: odom
  robot_base_frame: base_footprint
  
  # 更新和发布频率
  update_frequency: 5.0
  publish_frequency: 2.0
  
  # 地图参数
  static_map: false
  rolling_window: true
  width: 6.0
  height: 6.0
  resolution: 0.05
  
  # 变换参数
  transform_tolerance: 0.5
  
  # 插件配置
  plugins:
    - {name: obstacle_layer, type: "costmap_2d::ObstacleLayer"}
    - {name: inflation_layer, type: "costmap_2d::InflationLayer"}
