# 机器人导航仿真项目使用指南

## 项目概述

这个项目实现了一个完整的机器人设计和导航仿真系统，包含：
- 使用URDF创建的机器人模型（底盘、轮子、雷达、摄像头）
- Gazebo仿真环境
- SLAM建图功能
- 自主导航系统

## 环境要求

### 推荐环境：WSL2 + Ubuntu 20.04 + ROS Noetic

1. **安装WSL2和Ubuntu 20.04**
```bash
# 在Windows PowerShell中运行
wsl --install -d Ubuntu-20.04
```

2. **安装ROS Noetic**
```bash
# 进入WSL2环境
wsl

# 安装ROS Noetic
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
sudo apt install curl -y
curl -s https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | sudo apt-key add -
sudo apt update
sudo apt install ros-noetic-desktop-full -y

# 设置环境
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

3. **安装依赖包**
```bash
# 安装基础工具
sudo apt install python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool build-essential -y
sudo rosdep init
rosdep update

# 安装项目所需的ROS包
sudo apt install ros-noetic-gazebo-ros-pkgs ros-noetic-gazebo-ros-control -y
sudo apt install ros-noetic-navigation ros-noetic-slam-gmapping -y
sudo apt install ros-noetic-move-base ros-noetic-amcl -y
sudo apt install ros-noetic-map-server ros-noetic-dwa-local-planner -y
sudo apt install ros-noetic-teleop-twist-keyboard -y
```

## 项目设置

### 1. 创建工作空间
```bash
mkdir -p ~/catkin_ws/src
cd ~/catkin_ws/
catkin_make
echo "source ~/catkin_ws/devel/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### 2. 复制项目文件
```bash
# 将项目文件复制到工作空间
cp -r /path/to/robot-design ~/catkin_ws/src/robot_navigation
cd ~/catkin_ws
catkin_make
source devel/setup.bash
```

## 使用步骤

### 步骤1：在Rviz中查看机器人模型

```bash
# 启动机器人描述
roslaunch robot_navigation robot_description.launch
```

这将启动：
- robot_state_publisher（发布机器人状态）
- joint_state_publisher（发布关节状态）
- Rviz（可视化工具）

**预期结果**：在Rviz中看到蓝色底盘、黑色轮子、红色雷达和绿色摄像头的机器人模型。

### 步骤2：在Gazebo中仿真机器人

```bash
# 启动Gazebo仿真
roslaunch robot_navigation gazebo_world.launch
```

这将启动：
- Gazebo仿真环境
- 机器人模型加载
- 物理引擎仿真

**预期结果**：在Gazebo中看到机器人在一个有墙壁和障碍物的环境中。

### 步骤3：测试传感器数据

```bash
# 在新终端中运行传感器数据读取器
rosrun robot_navigation sensor_data_reader.py
```

**预期结果**：终端中显示激光雷达、摄像头和里程计数据。

### 步骤4：测试机器人控制

```bash
# 方法1：使用键盘控制
rosrun teleop_twist_keyboard teleop_twist_keyboard.py

# 方法2：使用自定义控制器
rosrun robot_navigation robot_controller.py
```

**预期结果**：机器人可以响应键盘命令或执行预设的运动模式。

### 步骤5：SLAM建图

```bash
# 启动SLAM建图
roslaunch robot_navigation slam.launch
```

这将启动：
- Gazebo仿真环境
- gmapping SLAM算法
- Rviz可视化
- 键盘控制

**操作步骤**：
1. 在键盘控制终端中，使用WASD键控制机器人移动
2. 在Rviz中观察地图逐渐构建
3. 确保机器人探索整个环境

**保存地图**：
```bash
# 在新终端中保存地图
rosrun map_server map_saver -f ~/catkin_ws/src/robot_navigation/maps/my_map
```

### 步骤6：自主导航

```bash
# 启动导航系统（需要先有地图文件）
roslaunch robot_navigation navigation.launch
```

**操作步骤**：
1. 在Rviz中使用"2D Pose Estimate"工具设置机器人初始位置
2. 使用"2D Nav Goal"工具设置目标位置
3. 观察机器人自主导航到目标位置

## 故障排除

### 常见问题

1. **Gazebo启动慢**
   - 首次启动会下载模型，请耐心等待
   - 可以预先下载：`gazebo --verbose`

2. **找不到包错误**
   ```bash
   # 确保已经source工作空间
   source ~/catkin_ws/devel/setup.bash
   
   # 检查包是否存在
   rospack find robot_navigation
   ```

3. **传感器数据无法获取**
   ```bash
   # 检查话题是否发布
   rostopic list
   rostopic echo /scan
   rostopic echo /robot_camera/image_raw
   ```

4. **导航不工作**
   - 确保地图文件存在
   - 检查AMCL定位是否正常
   - 验证代价地图是否正确显示

### 调试命令

```bash
# 查看所有节点
rosnode list

# 查看话题
rostopic list

# 查看TF树
rosrun tf view_frames
evince frames.pdf

# 查看计算图
rqt_graph

# 监控系统状态
htop
```

## 学习要点

### 1. URDF机器人建模
- 理解link和joint的概念
- 掌握惯性矩阵的计算
- 学会添加传感器和执行器

### 2. Gazebo仿真
- 物理属性配置
- 传感器插件使用
- 世界文件创建

### 3. ROS导航栈
- 代价地图配置
- 路径规划算法
- 定位算法原理

### 4. SLAM技术
- 激光雷达数据处理
- 地图构建原理
- 同时定位与建图

## 扩展练习

1. **修改机器人模型**
   - 添加更多传感器
   - 改变机器人尺寸
   - 添加机械臂

2. **创建复杂环境**
   - 设计多层建筑
   - 添加动态障碍物
   - 创建室外环境

3. **优化导航参数**
   - 调整代价地图参数
   - 优化路径规划器
   - 改进定位精度

4. **实现高级功能**
   - 多机器人协作
   - 语音控制
   - 视觉导航

## 参考资源

- [ROS官方教程](http://wiki.ros.org/ROS/Tutorials)
- [Navigation Stack教程](http://wiki.ros.org/navigation/Tutorials)
- [Gazebo教程](http://gazebosim.org/tutorials)
- [URDF教程](http://wiki.ros.org/urdf/Tutorials)
