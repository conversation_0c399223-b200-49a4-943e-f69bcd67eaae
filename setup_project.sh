#!/bin/bash

# 机器人导航项目设置脚本
# 用于在catkin工作空间中正确设置项目

echo "=== 机器人导航项目设置 ==="

# 检查是否在catkin工作空间中
if [ ! -f "src/CMakeLists.txt" ]; then
    echo "错误：请在catkin工作空间根目录运行此脚本"
    echo "当前目录应该包含 src/ 文件夹"
    exit 1
fi

# 创建项目目录
PROJECT_DIR="src/robot_navigation"
echo "创建项目目录: $PROJECT_DIR"
mkdir -p $PROJECT_DIR

# 复制项目文件到正确位置
echo "复制项目文件..."

# 如果当前目录有项目文件，复制它们
if [ -f "package.xml" ]; then
    cp package.xml $PROJECT_DIR/
    cp CMakeLists.txt $PROJECT_DIR/
    cp README.md $PROJECT_DIR/
    cp USAGE_GUIDE.md $PROJECT_DIR/
    cp -r launch $PROJECT_DIR/ 2>/dev/null || true
    cp -r urdf $PROJECT_DIR/ 2>/dev/null || true
    cp -r worlds $PROJECT_DIR/ 2>/dev/null || true
    cp -r config $PROJECT_DIR/ 2>/dev/null || true
    cp -r scripts $PROJECT_DIR/ 2>/dev/null || true
    cp -r maps $PROJECT_DIR/ 2>/dev/null || true
    cp -r meshes $PROJECT_DIR/ 2>/dev/null || true
    echo "文件复制完成"
else
    echo "在当前目录未找到项目文件，请确保项目文件在正确位置"
fi

# 设置脚本权限
if [ -d "$PROJECT_DIR/scripts" ]; then
    chmod +x $PROJECT_DIR/scripts/*.py
    echo "设置脚本执行权限"
fi

# 编译项目
echo "编译项目..."
catkin_make

if [ $? -eq 0 ]; then
    echo "✓ 编译成功！"
    echo ""
    echo "下一步："
    echo "1. source devel/setup.bash"
    echo "2. roslaunch robot_navigation robot_description.launch"
else
    echo "✗ 编译失败，请检查错误信息"
fi

echo "=== 设置完成 ==="
