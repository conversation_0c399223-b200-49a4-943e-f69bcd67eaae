<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- 定义颜色材质 -->
  
  <!-- 红色材质 -->
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>

  <!-- 蓝色材质 -->
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>

  <!-- 绿色材质 -->
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>

  <!-- 黄色材质 -->
  <material name="yellow">
    <color rgba="0.8 0.8 0.0 1.0"/>
  </material>

  <!-- 黑色材质 -->
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>

  <!-- 白色材质 -->
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>

  <!-- 灰色材质 -->
  <material name="grey">
    <color rgba="0.5 0.5 0.5 1.0"/>
  </material>

  <!-- 橙色材质 -->
  <material name="orange">
    <color rgba="1.0 0.5 0.0 1.0"/>
  </material>

  <!-- Gazebo材质定义 -->
  <gazebo reference="base_link">
    <material>Gazebo/Blue</material>
  </gazebo>

  <gazebo reference="left_wheel">
    <material>Gazebo/Black</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <kp>500000.0</kp>
    <kd>10.0</kd>
    <minDepth>0.001</minDepth>
    <maxVel>0.1</maxVel>
    <fdir1>1 0 0</fdir1>
  </gazebo>

  <gazebo reference="right_wheel">
    <material>Gazebo/Black</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <kp>500000.0</kp>
    <kd>10.0</kd>
    <minDepth>0.001</minDepth>
    <maxVel>0.1</maxVel>
    <fdir1>1 0 0</fdir1>
  </gazebo>

  <gazebo reference="lidar_link">
    <material>Gazebo/Red</material>
  </gazebo>

  <gazebo reference="camera_link">
    <material>Gazebo/Green</material>
  </gazebo>

</robot>
