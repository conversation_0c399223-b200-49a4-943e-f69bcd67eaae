<?xml version="1.0"?>
<launch>
  <!-- 导航系统启动文件 -->
  
  <!-- 参数定义 -->
  <arg name="model" default="$(find robot_navigation)/urdf/robot.xacro"/>
  <arg name="world_name" default="$(find robot_navigation)/worlds/simple_world.world"/>
  <arg name="map_file" default="$(find robot_navigation)/maps/my_map.yaml"/>
  <arg name="open_rviz" default="true"/>
  <arg name="move_forward_only" default="false"/>
  
  <!-- 启动Gazebo仿真环境 -->
  <include file="$(find robot_navigation)/launch/gazebo_world.launch">
    <arg name="model" value="$(arg model)"/>
    <arg name="world_name" value="$(arg world_name)"/>
    <arg name="gui" value="true"/>
  </include>

  <!-- 地图服务器 -->
  <node name="map_server" pkg="map_server" type="map_server" args="$(arg map_file)"/>

  <!-- AMCL定位 -->
  <include file="$(find amcl)/examples/amcl_diff.launch"/>
  
  <!-- 或者使用自定义AMCL参数 -->
  <!--
  <node pkg="amcl" type="amcl" name="amcl" output="screen">
    <param name="odom_frame_id" value="odom"/>
    <param name="odom_model_type" value="diff"/>
    <param name="base_frame_id" value="base_footprint"/>
    <param name="global_frame_id" value="map"/>
    <param name="min_particles" value="500"/>
    <param name="max_particles" value="2000"/>
    <param name="kld_err" value="0.05"/>
    <param name="kld_z" value="0.99"/>
    <param name="odom_alpha1" value="0.2"/>
    <param name="odom_alpha2" value="0.2"/>
    <param name="odom_alpha3" value="0.8"/>
    <param name="odom_alpha4" value="0.2"/>
    <param name="laser_max_beams" value="60"/>
    <param name="laser_max_range" value="12.0"/>
    <param name="laser_z_hit" value="0.5"/>
    <param name="laser_z_short" value="0.05"/>
    <param name="laser_z_max" value="0.05"/>
    <param name="laser_z_rand" value="0.5"/>
    <param name="laser_sigma_hit" value="0.2"/>
    <param name="laser_lambda_short" value="0.1"/>
    <param name="laser_model_type" value="likelihood_field"/>
    <param name="laser_likelihood_max_dist" value="2.0"/>
    <param name="update_min_d" value="0.2"/>
    <param name="update_min_a" value="0.5"/>
    <param name="resample_interval" value="1"/>
    <param name="transform_tolerance" value="0.1"/>
    <param name="recovery_alpha_slow" value="0.0"/>
    <param name="recovery_alpha_fast" value="0.0"/>
  </node>
  -->

  <!-- move_base导航 -->
  <node pkg="move_base" type="move_base" respawn="false" name="move_base" output="screen">
    <rosparam file="$(find robot_navigation)/config/navigation/costmap_common_params.yaml" command="load" ns="global_costmap"/>
    <rosparam file="$(find robot_navigation)/config/navigation/costmap_common_params.yaml" command="load" ns="local_costmap"/>
    <rosparam file="$(find robot_navigation)/config/navigation/local_costmap_params.yaml" command="load"/>
    <rosparam file="$(find robot_navigation)/config/navigation/global_costmap_params.yaml" command="load"/>
    <rosparam file="$(find robot_navigation)/config/navigation/base_local_planner_params.yaml" command="load"/>
    <rosparam file="$(find robot_navigation)/config/navigation/move_base_params.yaml" command="load"/>
    
    <!-- 重新映射话题 -->
    <remap from="cmd_vel" to="cmd_vel"/>
    <remap from="odom" to="odom"/>
    <remap from="scan" to="scan"/>
    
    <param name="base_global_planner" value="global_planner/GlobalPlanner"/>
    <param name="planner_frequency" value="1.0"/>
    <param name="planner_patience" value="5.0"/>
    
    <param name="base_local_planner" value="dwa_local_planner/DWAPlannerROS"/>
    <param name="controller_frequency" value="10.0"/>
    <param name="controller_patience" value="3.0"/>
    
    <param name="clearing_rotation_allowed" value="false"/>
  </node>

  <!-- 启动Rviz进行可视化 -->
  <group if="$(arg open_rviz)">
    <node name="rviz" pkg="rviz" type="rviz" 
          args="-d $(find robot_navigation)/config/rviz/navigation.rviz" 
          required="true"/>
  </group>

</launch>
