<?xml version="1.0"?>
<launch>
  <!-- 机器人描述启动文件 -->
  
  <!-- 参数定义 -->
  <arg name="model" default="$(find robot_navigation)/urdf/robot.urdf"/>
  <arg name="gui" default="true"/>
  <arg name="rvizconfig" default="$(find robot_navigation)/config/rviz/robot_model.rviz"/>

  <!-- 加载机器人描述到参数服务器 -->
  <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>

  <!-- 启动robot_state_publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher">
    <param name="publish_frequency" type="double" value="50.0"/>
  </node>

  <!-- 启动joint_state_publisher -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <param name="use_gui" value="$(arg gui)"/>
  </node>

  <!-- 启动Rviz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rvizconfig)" required="true"/>

</launch>
