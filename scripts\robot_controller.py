#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机器人运动控制节点
提供基本的运动控制功能，包括前进、后退、转向等
"""

import rospy
import math
import numpy as np
from geometry_msgs.msg import Twist, PoseStamped
from nav_msgs.msg import Odometry
from sensor_msgs.msg import LaserScan
import tf
from tf.transformations import euler_from_quaternion

class RobotController:
    def __init__(self):
        """初始化机器人控制器"""
        rospy.init_node('robot_controller', anonymous=True)
        
        # 发布器
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        
        # 订阅器
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        self.laser_sub = rospy.Subscriber('/scan', LaserScan, self.laser_callback)
        
        # TF监听器
        self.tf_listener = tf.TransformListener()
        
        # 机器人状态
        self.current_pose = None
        self.current_twist = None
        self.laser_data = None
        
        # 控制参数
        self.max_linear_speed = 0.5  # 最大线速度 m/s
        self.max_angular_speed = 1.0  # 最大角速度 rad/s
        self.obstacle_threshold = 0.8  # 障碍物检测阈值 m
        
        rospy.loginfo("机器人控制器已启动")
    
    def odom_callback(self, data):
        """里程计回调函数"""
        self.current_pose = data.pose.pose
        self.current_twist = data.twist.twist
    
    def laser_callback(self, data):
        """激光雷达回调函数"""
        self.laser_data = data
    
    def get_current_position(self):
        """获取当前位置"""
        if self.current_pose is None:
            return None
        
        x = self.current_pose.position.x
        y = self.current_pose.position.y
        
        # 获取朝向角度
        orientation = self.current_pose.orientation
        euler = euler_from_quaternion([
            orientation.x, orientation.y, orientation.z, orientation.w
        ])
        yaw = euler[2]
        
        return {'x': x, 'y': y, 'yaw': yaw}
    
    def move_forward(self, distance, speed=0.2):
        """向前移动指定距离"""
        if self.current_pose is None:
            rospy.logwarn("无法获取当前位置，无法执行移动命令")
            return False
        
        start_pos = self.get_current_position()
        target_distance = distance
        
        cmd = Twist()
        cmd.linear.x = min(speed, self.max_linear_speed)
        
        rate = rospy.Rate(10)
        traveled_distance = 0
        
        rospy.loginfo(f"开始向前移动 {distance}m")
        
        while traveled_distance < target_distance and not rospy.is_shutdown():
            # 检查前方是否有障碍物
            if self.check_front_obstacle():
                rospy.logwarn("前方检测到障碍物，停止移动")
                break
            
            # 发布运动命令
            self.cmd_vel_pub.publish(cmd)
            
            # 计算已移动距离
            current_pos = self.get_current_position()
            if current_pos:
                dx = current_pos['x'] - start_pos['x']
                dy = current_pos['y'] - start_pos['y']
                traveled_distance = math.sqrt(dx*dx + dy*dy)
            
            rate.sleep()
        
        # 停止机器人
        self.stop()
        rospy.loginfo(f"移动完成，实际移动距离: {traveled_distance:.2f}m")
        return True
    
    def turn_angle(self, angle_degrees, angular_speed=0.5):
        """转向指定角度（度）"""
        if self.current_pose is None:
            rospy.logwarn("无法获取当前位置，无法执行转向命令")
            return False
        
        start_pos = self.get_current_position()
        target_angle = math.radians(angle_degrees)
        
        cmd = Twist()
        if angle_degrees > 0:
            cmd.angular.z = min(angular_speed, self.max_angular_speed)
        else:
            cmd.angular.z = -min(angular_speed, self.max_angular_speed)
        
        rate = rospy.Rate(10)
        turned_angle = 0
        
        rospy.loginfo(f"开始转向 {angle_degrees}°")
        
        while abs(turned_angle) < abs(target_angle) and not rospy.is_shutdown():
            # 发布运动命令
            self.cmd_vel_pub.publish(cmd)
            
            # 计算已转向角度
            current_pos = self.get_current_position()
            if current_pos:
                angle_diff = current_pos['yaw'] - start_pos['yaw']
                # 处理角度跳跃
                if angle_diff > math.pi:
                    angle_diff -= 2 * math.pi
                elif angle_diff < -math.pi:
                    angle_diff += 2 * math.pi
                turned_angle = angle_diff
            
            rate.sleep()
        
        # 停止机器人
        self.stop()
        rospy.loginfo(f"转向完成，实际转向角度: {math.degrees(turned_angle):.1f}°")
        return True
    
    def move_to_goal(self, target_x, target_y, tolerance=0.1):
        """移动到目标位置"""
        if self.current_pose is None:
            rospy.logwarn("无法获取当前位置，无法执行移动到目标命令")
            return False
        
        rate = rospy.Rate(10)
        
        rospy.loginfo(f"开始移动到目标位置: ({target_x:.2f}, {target_y:.2f})")
        
        while not rospy.is_shutdown():
            current_pos = self.get_current_position()
            if not current_pos:
                continue
            
            # 计算到目标的距离和角度
            dx = target_x - current_pos['x']
            dy = target_y - current_pos['y']
            distance = math.sqrt(dx*dx + dy*dy)
            
            # 检查是否到达目标
            if distance < tolerance:
                rospy.loginfo("已到达目标位置")
                self.stop()
                return True
            
            # 计算目标角度
            target_yaw = math.atan2(dy, dx)
            angle_diff = target_yaw - current_pos['yaw']
            
            # 处理角度跳跃
            if angle_diff > math.pi:
                angle_diff -= 2 * math.pi
            elif angle_diff < -math.pi:
                angle_diff += 2 * math.pi
            
            # 创建运动命令
            cmd = Twist()
            
            # 如果角度差较大，先转向
            if abs(angle_diff) > 0.1:
                cmd.angular.z = 0.5 * angle_diff
                cmd.linear.x = 0.1  # 慢速前进
            else:
                # 角度对准后，主要前进
                cmd.linear.x = min(0.3, distance)
                cmd.angular.z = 0.2 * angle_diff
            
            # 限制速度
            cmd.linear.x = min(cmd.linear.x, self.max_linear_speed)
            cmd.angular.z = max(-self.max_angular_speed, 
                              min(self.max_angular_speed, cmd.angular.z))
            
            # 检查障碍物
            if self.check_front_obstacle() and cmd.linear.x > 0:
                rospy.logwarn("前方有障碍物，停止前进")
                cmd.linear.x = 0
            
            # 发布命令
            self.cmd_vel_pub.publish(cmd)
            rate.sleep()
        
        return False
    
    def check_front_obstacle(self):
        """检查前方是否有障碍物"""
        if self.laser_data is None:
            return False
        
        ranges = np.array(self.laser_data.ranges)
        
        # 检查前方60度范围内的障碍物
        num_readings = len(ranges)
        front_start = int(num_readings * 5/12)  # 前方左侧30度
        front_end = int(num_readings * 7/12)    # 前方右侧30度
        
        front_ranges = ranges[front_start:front_end]
        valid_ranges = front_ranges[(front_ranges >= self.laser_data.range_min) & 
                                  (front_ranges <= self.laser_data.range_max)]
        
        if len(valid_ranges) > 0:
            min_distance = np.min(valid_ranges)
            return min_distance < self.obstacle_threshold
        
        return False
    
    def stop(self):
        """停止机器人"""
        cmd = Twist()
        cmd.linear.x = 0
        cmd.angular.z = 0
        self.cmd_vel_pub.publish(cmd)
    
    def circle_motion(self, radius=1.0, angular_speed=0.5, duration=10.0):
        """圆周运动"""
        linear_speed = angular_speed * radius
        
        cmd = Twist()
        cmd.linear.x = min(linear_speed, self.max_linear_speed)
        cmd.angular.z = min(angular_speed, self.max_angular_speed)
        
        rospy.loginfo(f"开始圆周运动，半径: {radius}m，持续时间: {duration}s")
        
        start_time = rospy.Time.now()
        rate = rospy.Rate(10)
        
        while (rospy.Time.now() - start_time).to_sec() < duration and not rospy.is_shutdown():
            if not self.check_front_obstacle():
                self.cmd_vel_pub.publish(cmd)
            else:
                rospy.logwarn("检测到障碍物，暂停圆周运动")
                self.stop()
                rospy.sleep(1)
            
            rate.sleep()
        
        self.stop()
        rospy.loginfo("圆周运动完成")
    
    def demo_movements(self):
        """演示各种运动"""
        rospy.loginfo("开始运动演示")
        
        # 1. 向前移动
        self.move_forward(1.0, 0.2)
        rospy.sleep(1)
        
        # 2. 左转90度
        self.turn_angle(90, 0.5)
        rospy.sleep(1)
        
        # 3. 向前移动
        self.move_forward(1.0, 0.2)
        rospy.sleep(1)
        
        # 4. 右转90度
        self.turn_angle(-90, 0.5)
        rospy.sleep(1)
        
        # 5. 回到起点
        current_pos = self.get_current_position()
        if current_pos:
            self.move_to_goal(0, 0)
        
        rospy.loginfo("运动演示完成")

if __name__ == '__main__':
    try:
        controller = RobotController()
        
        # 等待传感器数据
        rospy.sleep(2)
        
        # 运行演示
        controller.demo_movements()
        
        # 保持节点运行
        rospy.spin()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("机器人控制器已停止")
