#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
项目设置测试脚本
用于验证ROS环境和项目配置是否正确
"""

import os
import sys
import subprocess
import time

def run_command(command, timeout=10):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, 
                              text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def check_ros_environment():
    """检查ROS环境"""
    print("=== 检查ROS环境 ===")
    
    # 检查ROS_DISTRO
    ros_distro = os.environ.get('ROS_DISTRO')
    if ros_distro:
        print(f"✓ ROS版本: {ros_distro}")
    else:
        print("✗ 未检测到ROS环境变量")
        return False
    
    # 检查roscore
    success, stdout, stderr = run_command("which roscore")
    if success:
        print("✓ roscore命令可用")
    else:
        print("✗ roscore命令不可用")
        return False
    
    return True

def check_required_packages():
    """检查必需的ROS包"""
    print("\n=== 检查必需的ROS包 ===")
    
    required_packages = [
        'gazebo_ros',
        'navigation',
        'gmapping',
        'amcl',
        'move_base',
        'map_server',
        'robot_state_publisher',
        'joint_state_publisher',
        'rviz',
        'teleop_twist_keyboard'
    ]
    
    all_available = True
    for package in required_packages:
        success, stdout, stderr = run_command(f"rospack find {package}")
        if success:
            print(f"✓ {package}")
        else:
            print(f"✗ {package} - 未安装")
            all_available = False
    
    return all_available

def check_project_files():
    """检查项目文件"""
    print("\n=== 检查项目文件 ===")
    
    required_files = [
        'package.xml',
        'CMakeLists.txt',
        'urdf/robot.urdf',
        'urdf/robot.xacro',
        'launch/robot_description.launch',
        'launch/gazebo_world.launch',
        'launch/slam.launch',
        'launch/navigation.launch',
        'worlds/simple_world.world',
        'config/rviz/robot_model.rviz',
        'scripts/sensor_data_reader.py',
        'scripts/robot_controller.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def check_urdf_syntax():
    """检查URDF文件语法"""
    print("\n=== 检查URDF文件语法 ===")
    
    # 检查URDF文件
    success, stdout, stderr = run_command("check_urdf urdf/robot.urdf")
    if success:
        print("✓ URDF文件语法正确")
        return True
    else:
        print(f"✗ URDF文件语法错误: {stderr}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    # 这里可以添加更多的功能测试
    # 由于需要roscore运行，这里只做基本检查
    
    print("基本功能测试需要在ROS环境中运行")
    print("请按照USAGE_GUIDE.md中的步骤进行测试")
    
    return True

def print_next_steps():
    """打印下一步操作指南"""
    print("\n" + "="*50)
    print("下一步操作指南:")
    print("="*50)
    print("1. 如果所有检查都通过，可以开始使用项目")
    print("2. 首先启动: roslaunch robot_navigation robot_description.launch")
    print("3. 然后尝试: roslaunch robot_navigation gazebo_world.launch")
    print("4. 详细使用方法请参考 USAGE_GUIDE.md")
    print("5. 如果有检查失败，请按照README.md安装相应的依赖")

def main():
    """主函数"""
    print("机器人导航仿真项目 - 环境检查")
    print("="*50)
    
    # 检查各个组件
    checks = [
        ("ROS环境", check_ros_environment),
        ("必需包", check_required_packages),
        ("项目文件", check_project_files),
        ("URDF语法", check_urdf_syntax),
        ("基本功能", test_basic_functionality)
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"✗ {name}检查时出错: {e}")
            results[name] = False
    
    # 总结结果
    print("\n" + "="*50)
    print("检查结果总结:")
    print("="*50)
    
    all_passed = True
    for name, passed in results.items():
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有检查都通过！项目已准备就绪。")
    else:
        print("\n⚠️  有些检查未通过，请解决相关问题后再试。")
    
    print_next_steps()

if __name__ == '__main__':
    main()
