# 代价地图通用参数配置

# 机器人几何参数
robot_radius: 0.25  # 机器人半径（米）

# 障碍物层参数
obstacle_range: 2.5  # 障碍物检测范围（米）
raytrace_range: 3.0  # 光线追踪范围（米）

# 膨胀层参数
inflation_radius: 0.3  # 膨胀半径（米）
cost_scaling_factor: 10.0  # 代价缩放因子

# 观测源配置
observation_sources: laser_scan_sensor

# 激光雷达传感器配置
laser_scan_sensor:
  sensor_frame: lidar_link
  data_type: LaserScan
  topic: /scan
  marking: true
  clearing: true
  min_obstacle_height: 0.0
  max_obstacle_height: 2.0
  obstacle_range: 2.5
  raytrace_range: 3.0
  inf_is_valid: true

# 地图类型
map_type: costmap

# 变换容忍度
transform_tolerance: 0.2

# 发布频率
publish_frequency: 2.0

# 足迹参数（机器人轮廓）
footprint: [[-0.3, -0.2], [-0.3, 0.2], [0.3, 0.2], [0.3, -0.2]]

# 代价地图插件
plugins:
  - {name: static_layer, type: "costmap_2d::StaticLayer"}
  - {name: obstacle_layer, type: "costmap_2d::ObstacleLayer"}
  - {name: inflation_layer, type: "costmap_2d::InflationLayer"}
