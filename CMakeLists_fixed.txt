cmake_minimum_required(VERSION 3.0.2)
project(robot_navigation)

## 查找catkin宏和库
find_package(catkin REQUIRED COMPONENTS
  rospy
  roscpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf
  tf2
  tf2_ros
  urdf
  xacro
  gazebo_ros
)

## 系统依赖
find_package(Boost REQUIRED COMPONENTS system)

## catkin特定配置
catkin_package(
  CATKIN_DEPENDS 
    rospy 
    roscpp 
    std_msgs 
    sensor_msgs 
    geometry_msgs 
    nav_msgs 
    tf 
    tf2 
    tf2_ros 
    urdf 
    xacro 
    gazebo_ros
)

## 指定额外的头文件位置
include_directories(
  ${catkin_INCLUDE_DIRS}
)

## 安装Python脚本
catkin_install_python(PROGRAMS
  scripts/robot_controller.py
  scripts/sensor_data_reader.py
  scripts/test_setup.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

## 安装启动文件
install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  PATTERN ".svn" EXCLUDE
)

## 安装配置文件
install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
  PATTERN ".svn" EXCLUDE
)

## 安装URDF文件
install(DIRECTORY urdf/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/urdf
  PATTERN ".svn" EXCLUDE
)

## 安装世界文件
install(DIRECTORY worlds/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/worlds
  PATTERN ".svn" EXCLUDE
)

## 安装地图文件
install(DIRECTORY maps/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/maps
  PATTERN ".svn" EXCLUDE
)
