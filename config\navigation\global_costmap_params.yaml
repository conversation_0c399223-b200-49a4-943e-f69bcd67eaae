# 全局代价地图参数配置

global_costmap:
  # 全局参数
  global_frame: map
  robot_base_frame: base_footprint
  
  # 更新和发布频率
  update_frequency: 1.0
  publish_frequency: 0.5
  
  # 地图参数
  static_map: true
  rolling_window: false
  
  # 变换参数
  transform_tolerance: 0.5
  
  # 插件配置
  plugins:
    - {name: static_layer, type: "costmap_2d::StaticLayer"}
    - {name: obstacle_layer, type: "costmap_2d::ObstacleLayer"}
    - {name: inflation_layer, type: "costmap_2d::InflationLayer"}
