<?xml version="1.0"?>
<launch>
  <!-- SLAM建图启动文件 -->
  
  <!-- 参数定义 -->
  <arg name="model" default="$(find robot_navigation)/urdf/robot.xacro"/>
  <arg name="world_name" default="$(find robot_navigation)/worlds/simple_world.world"/>
  <arg name="slam_method" default="gmapping"/>
  <arg name="open_rviz" default="true"/>
  
  <!-- 启动Gazebo仿真环境 -->
  <include file="$(find robot_navigation)/launch/gazebo_world.launch">
    <arg name="model" value="$(arg model)"/>
    <arg name="world_name" value="$(arg world_name)"/>
    <arg name="gui" value="true"/>
  </include>

  <!-- SLAM算法选择 -->
  <group if="$(eval slam_method == 'gmapping')">
    <!-- 使用gmapping进行SLAM -->
    <node pkg="gmapping" type="slam_gmapping" name="slam_gmapping" output="screen">
      <!-- 基本参数 -->
      <param name="base_frame" value="base_footprint"/>
      <param name="odom_frame" value="odom"/>
      <param name="map_frame" value="map"/>
      
      <!-- 传感器参数 -->
      <param name="maxUrange" value="5.0"/>
      <param name="maxRange" value="8.0"/>
      <param name="sigma" value="0.05"/>
      <param name="kernelSize" value="1"/>
      <param name="lstep" value="0.05"/>
      <param name="astep" value="0.05"/>
      <param name="iterations" value="5"/>
      <param name="lsigma" value="0.075"/>
      <param name="ogain" value="3.0"/>
      <param name="lskip" value="0"/>
      
      <!-- 运动模型参数 -->
      <param name="srr" value="0.01"/>
      <param name="srt" value="0.02"/>
      <param name="str" value="0.01"/>
      <param name="stt" value="0.02"/>
      
      <!-- 地图参数 -->
      <param name="linearUpdate" value="0.1"/>
      <param name="angularUpdate" value="0.1"/>
      <param name="temporalUpdate" value="0.5"/>
      <param name="resampleThreshold" value="0.5"/>
      <param name="particles" value="80"/>
      
      <!-- 地图尺寸 -->
      <param name="xmin" value="-10.0"/>
      <param name="ymin" value="-10.0"/>
      <param name="xmax" value="10.0"/>
      <param name="ymax" value="10.0"/>
      <param name="delta" value="0.05"/>
      
      <!-- 似然参数 -->
      <param name="llsamplerange" value="0.01"/>
      <param name="llsamplestep" value="0.01"/>
      <param name="lasamplerange" value="0.005"/>
      <param name="lasamplestep" value="0.005"/>
      
      <!-- 输出参数 -->
      <param name="transform_publish_period" value="0.05"/>
      <param name="occ_thresh" value="0.25"/>
      <param name="minimumScore" value="50"/>
    </node>
  </group>

  <group if="$(eval slam_method == 'cartographer')">
    <!-- 使用Cartographer进行SLAM -->
    <node name="cartographer_node" pkg="cartographer_ros"
          type="cartographer_node" args="
              -configuration_directory $(find robot_navigation)/config/slam
              -configuration_basename robot_2d.lua"
          output="screen">
      <remap from="scan" to="scan"/>
      <remap from="odom" to="odom"/>
    </node>

    <node name="cartographer_occupancy_grid_node" pkg="cartographer_ros"
          type="cartographer_occupancy_grid_node" args="-resolution 0.05"/>
  </group>

  <!-- 启动Rviz进行可视化 -->
  <group if="$(arg open_rviz)">
    <node name="rviz" pkg="rviz" type="rviz" 
          args="-d $(find robot_navigation)/config/rviz/slam.rviz" 
          required="true"/>
  </group>

  <!-- 启动键盘控制 -->
  <node pkg="teleop_twist_keyboard" type="teleop_twist_keyboard.py" name="teleop" output="screen">
    <param name="scale_linear" value="0.3"/>
    <param name="scale_angular" value="0.5"/>
  </node>

</launch>
