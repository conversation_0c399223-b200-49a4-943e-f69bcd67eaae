cmake_minimum_required(VERSION 3.0.2)
project(robot_simulation)

## 查找catkin宏和库
find_package(catkin REQUIRED COMPONENTS
  rospy
  roscpp
  std_msgs
  geometry_msgs
  sensor_msgs
  nav_msgs
  tf2
  tf2_ros
  urdf
  xacro
  robot_state_publisher
  joint_state_publisher
  gazebo_ros
  gazebo_ros_control
  gazebo_plugins
  controller_manager
  diff_drive_controller
  joint_state_controller
  move_base
  amcl
  map_server
  gmapping
)

## 系统依赖
find_package(Boost REQUIRED COMPONENTS system)

## catkin特定配置
catkin_package(
  CATKIN_DEPENDS 
    rospy 
    roscpp 
    std_msgs 
    geometry_msgs 
    sensor_msgs 
    nav_msgs 
    tf2 
    tf2_ros 
    urdf 
    xacro 
    robot_state_publisher 
    joint_state_publisher 
    gazebo_ros 
    gazebo_ros_control 
    gazebo_plugins
    controller_manager
    diff_drive_controller
    joint_state_controller
    move_base
    amcl
    map_server
    gmapping
)

## 包含目录
include_directories(
  ${catkin_INCLUDE_DIRS}
)

## 安装launch文件
install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  PATTERN ".svn" EXCLUDE
)

## 安装URDF文件
install(DIRECTORY urdf/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/urdf
  PATTERN ".svn" EXCLUDE
)

## 安装世界文件
install(DIRECTORY worlds/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/worlds
  PATTERN ".svn" EXCLUDE
)

## 安装配置文件
install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
  PATTERN ".svn" EXCLUDE
)

## 安装地图文件
install(DIRECTORY maps/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/maps
  PATTERN ".svn" EXCLUDE
)

## 安装Rviz配置文件
install(DIRECTORY rviz/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/rviz
  PATTERN ".svn" EXCLUDE
)
