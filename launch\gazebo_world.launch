<?xml version="1.0"?>
<launch>
  <!-- Gazebo仿真环境启动文件 -->
  
  <!-- 参数定义 -->
  <arg name="model" default="$(find robot_navigation)/urdf/robot.xacro"/>
  <arg name="world_name" default="$(find robot_navigation)/worlds/simple_world.world"/>
  <arg name="x_pos" default="0.0"/>
  <arg name="y_pos" default="0.0"/>
  <arg name="z_pos" default="0.0"/>
  <arg name="roll" default="0.0"/>
  <arg name="pitch" default="0.0"/>
  <arg name="yaw" default="0.0"/>
  <arg name="gui" default="true"/>
  <arg name="paused" default="false"/>
  <arg name="use_sim_time" default="true"/>
  <arg name="headless" default="false"/>
  <arg name="debug" default="false"/>

  <!-- 设置使用仿真时间 -->
  <param name="/use_sim_time" value="$(arg use_sim_time)"/>

  <!-- 启动Gazebo空世界 -->
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(arg world_name)"/>
    <arg name="debug" value="$(arg debug)"/>
    <arg name="gui" value="$(arg gui)"/>
    <arg name="paused" value="$(arg paused)"/>
    <arg name="use_sim_time" value="$(arg use_sim_time)"/>
    <arg name="headless" value="$(arg headless)"/>
  </include>

  <!-- 加载机器人描述到参数服务器 -->
  <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>

  <!-- 启动robot_state_publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher">
    <param name="publish_frequency" type="double" value="50.0"/>
  </node>

  <!-- 在Gazebo中生成机器人 -->
  <node name="urdf_spawner" pkg="gazebo_ros" type="spawn_model" respawn="false" output="screen"
        args="-urdf -model mobile_robot -param robot_description 
              -x $(arg x_pos) -y $(arg y_pos) -z $(arg z_pos)
              -R $(arg roll) -P $(arg pitch) -Y $(arg yaw)"/>

  <!-- 启动joint_state_publisher -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <param name="use_gui" value="false"/>
  </node>

</launch>
